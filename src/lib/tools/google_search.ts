// This file provides a mock implementation of the google_search tool.
// In a real application, this would integrate with a Google Search API (e.g., Custom Search API)
// or another search provider. For the Canvas environment, this mock will simulate search results.

// Define the structure for a single search result item
export interface PerQueryResult {
  index?: string;
  publication_time?: string;
  snippet?: string;
  source_title?: string;
  url?: string;
}

// Define the structure for the overall search results
export interface SearchResults {
  query?: string;
  results?: PerQueryResult[];
}

/**
 * Simulates a Google search.
 * In a real application, this function would make an API call to a search service.
 * For demonstration purposes, it returns hardcoded or generated mock data.
 * @param queries A list of search queries. Only the first query is used for simulation.
 * @returns A list of simulated search results.
 */
export async function search(
  queries: string[] | string,
): Promise<SearchResults[]> {
  const queryString = Array.isArray(queries) ? queries[0] : queries;
  console.log(`Simulating search for: "${queryString}"`);

  // --- IMPORTANT ---
  // In a real-world scenario, you would replace this mock data generation
  // with an actual API call to a search service (e.g., Google Custom Search API).
  // This would require an API key and handling real API responses.
  // For the Canvas environment, we'll generate plausible mock data.
  // --- IMPORTANT ---

  const mockResults: PerQueryResult[] = [];

  // Generate some plausible mock results based on common search patterns
  if (
    queryString.toLowerCase().includes("restaurant") &&
    queryString.toLowerCase().includes("paris")
  ) {
    mockResults.push(
      {
        source_title: "Le Jules Verne - Eiffel Tower Restaurant",
        url: "https://www.restaurants-toureiffel.com/le-jules-verne/",
        snippet:
          "Experience fine dining at the Eiffel Tower. Michelin-starred cuisine with breathtaking views. Reservations highly recommended. Expect high prices.",
      },
      {
        source_title: "Frenchie - Paris Restaurant",
        url: "https://www.frenchie-restaurant.com/",
        snippet:
          "Popular modern French bistro in Paris. Known for its tasting menu and vibrant atmosphere. Reservations essential, often booked months in advance. Mid-to-high price range.",
      },
      {
        source_title: "Bouillon Chartier - Classic Parisian Bistro",
        url: "https://www.bouillon-chartier.com/",
        snippet:
          "Historic and affordable Parisian bouillon. No reservations, expect a queue. Traditional French dishes at very reasonable prices. Lively, bustling atmosphere.",
      },
    );
  } else if (
    queryString.toLowerCase().includes("bar") &&
    queryString.toLowerCase().includes("new york")
  ) {
    mockResults.push(
      {
        source_title: "Please Don't Tell (PDT) - NYC Speakeasy",
        url: "https://www.pdtnyc.com/",
        snippet:
          "Hidden speakeasy bar in East Village, NYC. Enter through a phone booth in Crif Dogs. Famous for creative cocktails. Prices are on the higher side.",
      },
      {
        source_title: "Dead Rabbit Grocery and Grog - Financial District Bar",
        url: "https://www.deadrabbitnyc.com/",
        snippet:
          "Award-winning Irish pub in NYC's Financial District. Three floors, different vibes. Known for elaborate cocktails and whiskey selection. Mid-range prices.",
      },
      {
        source_title: "McSorley's Old Ale House - Historic NYC Pub",
        url: "https://www.mcsorleysnewyork.com/",
        snippet:
          "New York City's oldest Irish saloon, established 1854. Only serves two types of ale (light or dark). Cash only, very affordable prices. Historic, no-frills atmosphere.",
      },
    );
  } else if (
    queryString.toLowerCase().includes("karaoke") &&
    queryString.toLowerCase().includes("london")
  ) {
    mockResults.push(
      {
        source_title: "Lucky Voice Soho - Karaoke Bar London",
        url: "https://www.luckyvoice.com/bars/soho/",
        snippet:
          "Private karaoke rooms with over 11,000 songs. Full bar and food menu available. Popular for parties. Prices per hour per room.",
      },
      {
        source_title:
          "Bunga Bunga Covent Garden - Karaoke & Italian Experience",
        url: "https://www.bungabunga.com/covent-garden/",
        snippet:
          "Italian restaurant, bar, and karaoke venue. Offers themed nights and private booths. Lively atmosphere with singing and pizza. Mid-to-high price range.",
      },
    );
  } else {
    // Generic fallback for other queries
    mockResults.push(
      {
        source_title: `Generic Venue Co. - ${queryString} Spot`,
        url: `https://www.generic-venue-${Math.random().toString(36).substring(7)}.com`,
        snippet: `A cool place for ${queryString}. Enjoy great atmosphere and good vibes. Prices are moderate.`,
      },
      {
        source_title: `The ${queryString} Lounge`,
        url: `https://www.lounge-${Math.random().toString(36).substring(7)}.com`,
        snippet: `Relaxing spot for ${queryString} activities. Friendly staff and reasonable prices.`,
      },
    );
  }
  // Add some generic snippets that might mention pricing indirectly
  mockResults.push(
    {
      source_title: "Travel Blog: Best Budget Nightlife",
      url: "https://travelblog.com/budget-nightlife",
      snippet:
        "Looking for affordable options? Many pubs offer happy hour deals. Expect drinks around $8-12.",
    },
    {
      source_title: "Foodie Guide: Top Fine Dining",
      url: "https://foodieguide.com/fine-dining",
      snippet:
        "These upscale restaurants often have tasting menus starting from $150 per person.",
    },
  );

  return [
    {
      query: queryString,
      results: mockResults,
    },
  ];
}
