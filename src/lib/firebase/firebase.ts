import { initializeApp, getApps, getApp } from "firebase/app";
import {
  Auth,
  getAuth,
  signInWithCustomToken,
  signInAnonymously,
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// Declare the special global __initial_auth_token provided by the Canvas environment.
// This is used for authentication in the Canvas.
declare const __initial_auth_token: string | undefined;

// Safely get the initial auth token from the global scope.
const initialAuthToken =
  typeof __initial_auth_token !== "undefined"
    ? __initial_auth_token
    : undefined;

// Consistently get Firebase config values from process.env for Next.js environment.
// The Canvas environment is expected to map its internal __app_id to NEXT_PUBLIC_FIREBASE_APP_ID.
const FIREBASE_API_KEY = process.env.NEXT_PUBLIC_FIREBASE_API_KEY;
const FIREBASE_AUTH_DOMAIN = process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN;
const FIREBASE_PROJECT_ID = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;
const FIREBASE_STORAGE_BUCKET = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET;
const FIREBASE_MESSAGING_SENDER_ID =
  process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID;
const FIREBASE_APP_ID = process.env.NEXT_PUBLIC_FIREBASE_APP_ID; // Use process.env for __app_id
const FIREBASE_MEASUREMENT_ID = process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID;

const firebaseConfig = {
  apiKey: FIREBASE_API_KEY,
  authDomain: FIREBASE_AUTH_DOMAIN,
  projectId: FIREBASE_PROJECT_ID,
  storageBucket: FIREBASE_STORAGE_BUCKET,
  messagingSenderId: FIREBASE_MESSAGING_SENDER_ID,
  appId: FIREBASE_APP_ID,
  measurementId: FIREBASE_MEASUREMENT_ID,
};

// Validate essential config to catch issues early
if (
  !firebaseConfig.apiKey ||
  !firebaseConfig.projectId ||
  !firebaseConfig.appId
) {
  console.error(
    "Firebase configuration environment variables are missing or incomplete!",
  );
  // In a production app, you might want to throw an error here to prevent startup.
}

// Initialize Firebase
// Cast to any to avoid strict TypeScript errors if some config keys are undefined
const app = !getApps().length ? initializeApp(firebaseConfig as any) : getApp();
const auth = getAuth(app);
const db = getFirestore(app);

// Export a promise that resolves with the Firebase Auth instance when authentication is ready
const authReadyPromise = (async (): Promise<Auth> => {
  if (initialAuthToken) {
    try {
      await signInWithCustomToken(auth, initialAuthToken);
      console.log("Signed in with custom token.");
    } catch (error) {
      console.error("Error signing in with custom token:", error);
      // Fallback to anonymous if custom token fails
      try {
        await signInAnonymously(auth);
        console.log("Signed in anonymously due to custom token error.");
      } catch (anonError) {
        console.error("Error signing in anonymously:", anonError);
      }
    }
  } else {
    // For local development or if no custom token is provided
    try {
      await signInAnonymously(auth);
      console.log("Signed in anonymously.");
    } catch (anonError) {
      console.error("Error signing in anonymously:", anonError);
    }
  }
  return auth; // Return the auth instance itself
})(); // Immediately invoke the async function to start the auth process

// Export the app ID using the process.env value, aliased as __app_id for consistency
export { app, auth, db, FIREBASE_APP_ID as __app_id, authReadyPromise };
