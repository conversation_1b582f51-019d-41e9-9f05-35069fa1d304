'use client'; // This directive marks the component as a Client Component

import { useState } from 'react';
import { Input } from '@/components/ui/input'; // Assuming Shadcn UI input component path
import { Button } from '@/components/ui/button'; // Assuming Shadcn UI button component path
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'; // Shadcn UI Card components
// Import Shadcn Select components
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Venue {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  types?: string[];
  estimatedCostPerPerson?: number;
  averageDurationMinutes?: number;
}

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [locationQuery, setLocationQuery] = useState<string>(''); // New state for location
  const [venueType, setVenueType] = useState<string>('all'); // Initialize with 'all'
  const [venues, setVenues] = useState<Venue[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Define common venue types for the dropdown
  const venueTypes = [
    { value: 'all', label: 'All Types' }, // FIX: Changed value from '' to 'all'
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'bar', label: 'Bar' },
    { value: 'night_club', label: 'Night Club' },
    { value: 'cafe', label: 'Cafe' },
    { value: 'karaoke', label: 'Karaoke' },
    { value: 'sports_club', label: 'Sports Club' }, // General sports venue
    { value: 'amusement_park', label: 'Amusement Park' }, // Could include karting, etc.
    { value: 'bowling_alley', label: 'Bowling Alley' },
    { value: 'museum', label: 'Museum' }, // Example of another type
  ];

  const handleSearch = async () => {
    setError(null);
    setLoading(true);
    setVenues([]); // Clear previous results

    try {
      // Construct the API URL with query, location, and type parameters
      const params = new URLSearchParams();
      if (searchQuery.trim()) {
        params.append('query', searchQuery.trim());
      }
      if (locationQuery.trim()) {
        params.append('location', locationQuery.trim());
      }
      // Only append type if it's not 'all'
      if (venueType && venueType !== 'all') { // FIX: Check for 'all'
        params.append('type', venueType);
      }

      // If no search parameters are provided, don't make the API call
      if (!searchQuery.trim() && !locationQuery.trim() && (venueType === 'all' || !venueType)) {
        setError('Please enter a search query, location, or select a specific venue type.');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/osm/search?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch venues');
      }

      const data = await response.json();
      if (data.places && Array.isArray(data.places)) {
        setVenues(data.places);
      } else {
        setVenues([]);
        setError('No places found or unexpected data format.');
      }
    } catch (err: any) {
      console.error('Search error:', err);
      setError(err.message || 'An unexpected error occurred during search.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 text-gray-50 flex flex-col items-center p-4 sm:p-8">
      {/* Header */}
      <h1 className="text-4xl sm:text-5xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">
        NightLife Planner
      </h1>

      {/* Search Input and Button */}
      <div className="w-full max-w-lg flex flex-col gap-4 mb-8 p-4 bg-gray-900 rounded-xl shadow-lg border border-gray-800">
        <Input
          type="text"
          placeholder="e.g., 'pizza', 'live music'"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-gray-800 border-gray-700 text-gray-50 placeholder:text-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all duration-200"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        />
        <Input
          type="text"
          placeholder="Location (e.g., 'Paris', 'New York City')"
          value={locationQuery}
          onChange={(e) => setLocationQuery(e.target.value)}
          className="bg-gray-800 border-gray-700 text-gray-50 placeholder:text-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all duration-200"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        />
        <Select value={venueType} onValueChange={setVenueType}>
          <SelectTrigger className="w-full bg-gray-800 border-gray-700 text-gray-50 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all duration-200">
            <SelectValue placeholder="Select a venue type" />
          </SelectTrigger>
          <SelectContent className="bg-gray-800 border-gray-700 text-gray-50">
            {venueTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          onClick={handleSearch}
          disabled={loading || (!searchQuery.trim() && !locationQuery.trim() && venueType === 'all')} // FIX: Check for 'all'
          className="w-full bg-gradient-to-r from-blue-600 to-purple-700 hover:from-blue-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Searching...' : 'Search'}
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <p className="text-red-400 mb-4 text-center">Error: {error}</p>
      )}

      {/* Search Results Display */}
      <div className="w-full max-w-3xl grid grid-cols-1 md:grid-cols-2 gap-6">
        {venues.length === 0 && !loading && !error && (searchQuery.trim() || locationQuery.trim() || venueType !== 'all') && ( // FIX: Check for 'all'
          <p className="text-gray-400 text-center col-span-full">No venues found for your criteria. Try a different query!</p>
        )}
        {venues.length === 0 && !loading && !error && !searchQuery.trim() && !locationQuery.trim() && venueType === 'all' && ( // FIX: Check for 'all'
          <p className="text-gray-400 text-center col-span-full">Start by searching for a place or selecting a type.</p>
        )}

        {venues.map((venue) => (
          <Card key={venue.id} className="bg-gray-800 border border-gray-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
            <CardHeader className="p-4 border-b border-gray-700">
              <CardTitle className="text-xl font-semibold text-blue-300">
                {venue.name}
              </CardTitle>
              <CardDescription className="text-gray-400 text-sm">
                {venue.address}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-4 text-gray-300 text-sm">
              <p className="mb-1">
                <span className="font-medium text-gray-200">Type(s):</span> {venue.types && venue.types.length > 0 ? venue.types.join(', ') : 'N/A'}
              </p>
              <p className="mb-1">
                <span className="font-medium text-gray-200">Est. Cost:</span> ${venue.estimatedCostPerPerson ? venue.estimatedCostPerPerson.toFixed(2) : 'N/A'}
              </p>
              <p>
                <span className="font-medium text-gray-200">Avg. Duration:</span> {venue.averageDurationMinutes ? `${venue.averageDurationMinutes} mins` : 'N/A'}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
