import { NextResponse } from "next/server";
import { db, __app_id } from "../../../../lib/firebase/firebase"; // Adjust path if your firebase.ts is elsewhere
import { doc, setDoc, getDoc } from "firebase/firestore";
import { VenueData } from "@/types/routine"; // Import VenueData from shared types

// Import the google_search tool
// Ensure this path is correct and the google_search tool is available
import * as google_search from "../../../../lib/tools/google_search";

// Function to save or update venue data in Firestore
async function saveOrUpdateVenue(venue: VenueData) {
  if (!__app_id) {
    console.error(
      "App ID not available for Firestore path. Cannot save venue.",
    );
    return;
  }
  const venueRef = doc(
    db,
    `artifacts/${__app_id}/public/data/venues`,
    venue.id,
  );

  try {
    const docSnap = await getDoc(venueRef);

    if (docSnap.exists()) {
      // Update existing document
      await setDoc(
        venueRef,
        { ...venue, lastFetched: new Date() },
        { merge: true },
      );
      console.log(`Updated venue in Firestore: ${venue.name}`);
    } else {
      // Create new document
      await setDoc(venueRef, { ...venue, lastFetched: new Date() });
      console.log(`Added new venue to Firestore: ${venue.name}`);
    }
  } catch (e) {
    console.error("Error saving/updating venue to Firestore:", e);
  }
}

// Helper for exponential backoff for LLM API calls
async function fetchWithExponentialBackoff(
  url: string,
  options: RequestInit,
  retries = 3,
  delay = 1000,
) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        // If it's a 4xx error (e.g., bad request), don't retry, just throw
        if (response.status >= 400 && response.status < 500) {
          const errorData = await response.json();
          throw new Error(
            `Client error (${response.status}): ${JSON.stringify(errorData)}`,
          );
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response;
    } catch (error) {
      console.warn(
        `Attempt ${i + 1} failed: ${error}. Retrying in ${delay}ms...`,
      );
      if (i < retries - 1) {
        await new Promise((res) => setTimeout(res, delay));
        delay *= 2; // Exponential backoff
      } else {
        throw error; // Re-throw if max retries reached
      }
    }
  }
  throw new Error("Max retries exceeded for LLM API call.");
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get("query");
  const location = searchParams.get("location");
  const type = searchParams.get("type");
  const limit = parseInt(searchParams.get("limit") || "5", 10); // Limit number of venues to process

  // Construct the search query for the google_search tool
  let searchQueryForTool = `${query || ""} ${type && type !== "all" ? type : ""} ${location || ""} nightlife venues menu prices`; // Added "menu prices"
  searchQueryForTool = searchQueryForTool.trim();

  if (!searchQueryForTool) {
    return NextResponse.json(
      {
        error:
          "Search query, location, or type is required to perform a web search.",
      },
      { status: 400 },
    );
  }

  try {
    // Step 1: Use google_search to find relevant information
    const searchResults = await google_search.search(searchQueryForTool);

    if (
      !searchResults ||
      searchResults.length === 0 ||
      !searchResults[0].results ||
      searchResults[0].results.length === 0
    ) {
      return NextResponse.json(
        { error: "No relevant information found via web search." },
        { status: 404 },
      );
    }

    // Combine snippets from search results to provide context to the LLM
    const contextFromSearch = searchResults[0].results
      .slice(0, Math.min(limit * 2, searchResults[0].results.length)) // Take a few more results than limit for context
      .map(
        (res) =>
          `Source: ${res.source_title}\nURL: ${res.url}\nSnippet: ${res.snippet}`,
      )
      .join("\n\n---\n\n");

    // Step 2: Construct a prompt for the LLM to extract and structure data
    const prompt = `Based on the following search results about nightlife venues, extract and structure information for up to ${limit} distinct venues.
    For each venue, provide:
    - A unique ID (string, generate if not explicit)
    - A name (string)
    - A plausible address (string)
    - Latitude (number, estimate if not explicit, keep realistic for the location)
    - Longitude (number, estimate if not explicit, keep realistic for the location)
    - An array of types (strings, e.g., ["restaurant", "bar", "live_music"])
    - An estimated cost per person (number, between 15 and 100, **infer from context like 'affordable', 'upscale', 'cheap', or venue type/description if direct prices are not found. If no inference is possible, use a reasonable default based on venue type, e.g., 20 for bars, 40 for casual restaurants, 60 for clubs.**)
    - An average duration in minutes (number, between 60 and 240, infer from context if possible, otherwise use a reasonable default based on venue type, e.g., 90 for restaurants, 120 for bars.)
    - A website URL (string, if found)
    - A phone number (string, if found).

    If a piece of information is not available or cannot be reasonably inferred, omit that property.
    Ensure the response is a JSON array of these venue objects.

    Search Results:
    ${contextFromSearch}`;

    // --- START OpenRouter API Integration for Google Gemini Flash ---
    const openRouterApiKey =
      process.env.NEXT_PUBLIC_OPENROUTER_DEEPSEEK_API_KEY; // This key is for OpenRouter
    const openRouterApiUrl = "https://openrouter.ai/api/v1/chat/completions";

    const openRouterPayload = {
      model: "google/gemini-2.0-flash-exp:free", // Changed model to Google Gemini Flash
      messages: [
        {
          role: "user",
          // For text-only input, content is a string.
          // The image_url type is for multimodal inputs, which is not our current use case here.
          content: prompt,
        },
        {
          role: "system",
          content: `You are a helpful assistant that extracts and formats venue information into a JSON array. Ensure the output strictly adheres to the provided JSON schema. If a field is not found, omit it unless a default is specified in the user prompt.`,
        },
      ],
      response_format: { type: "json_object" }, // Request JSON output
    };

    const response = await fetchWithExponentialBackoff(openRouterApiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${openRouterApiKey}`,
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000", // Optional: Your site URL
        "X-Title": "NightLife Planner", // Optional: Your site name
        "Content-Type": "application/json",
      },
      body: JSON.stringify(openRouterPayload),
    });

    const result = await response.json();

    // OpenRouter/Gemini response structure for chat completions
    if (
      !result.choices ||
      result.choices.length === 0 ||
      !result.choices[0].message ||
      !result.choices[0].message.content
    ) {
      throw new Error(
        "LLM response structure unexpected or empty from OpenRouter (Gemini).",
      );
    }

    const llmJsonText = result.choices[0].message.content;

    // Robust JSON parsing to handle potential non-array responses from LLM
    let generatedVenues: VenueData[] = [];
    let parsedResult;
    try {
      parsedResult = JSON.parse(llmJsonText);
    } catch (parseError) {
      console.error("Failed to parse LLM JSON response:", parseError);
      throw new Error("Invalid JSON response from LLM.");
    }

    if (Array.isArray(parsedResult)) {
      generatedVenues = parsedResult;
    } else if (typeof parsedResult === "object" && parsedResult !== null) {
      // Check for common keys where the LLM might wrap the array
      if (Array.isArray(parsedResult.venues)) {
        generatedVenues = parsedResult.venues;
      } else if (Array.isArray(parsedResult.places)) {
        // Also check for 'places' as we used that key before
        generatedVenues = parsedResult.places;
      } else {
        // If it's a single object that looks like a VenueData, wrap it in an array
        // This is a heuristic and might need refinement depending on LLM behavior
        if (
          parsedResult.id &&
          parsedResult.name &&
          parsedResult.address &&
          parsedResult.types
        ) {
          generatedVenues = [parsedResult as VenueData];
        } else {
          console.warn(
            "LLM returned a non-array, non-wrapped object that doesn't resemble a VenueData:",
            parsedResult,
          );
          generatedVenues = []; // Default to empty if structure is unexpected
        }
      }
    } else {
      console.warn("LLM returned non-JSON or unexpected type:", parsedResult);
      generatedVenues = []; // Default to empty if not array or object
    }
    // --- END OpenRouter API Integration ---

    // Process and save generated venues to Firestore
    const processedVenues: VenueData[] = [];
    for (const item of generatedVenues) {
      // This loop will now safely iterate over an array
      // Apply default values if LLM couldn't infer them
      const defaultCost = getDefaultCostForType(item.types);
      const defaultDuration = getDefaultDurationForType(item.types);

      const venueToSave: VenueData = {
        id: item.id,
        name: item.name,
        address: item.address,
        latitude: item.latitude,
        longitude: item.longitude,
        types: item.types,
        estimatedCostPerPerson:
          item.estimatedCostPerPerson !== undefined
            ? item.estimatedCostPerPerson
            : defaultCost,
        averageDurationMinutes:
          item.averageDurationMinutes !== undefined
            ? item.averageDurationMinutes
            : defaultDuration,
        website: item.website,
        phoneNumber: item.phoneNumber,
        lastFetched: new Date(),
      };
      await saveOrUpdateVenue(venueToSave);
      processedVenues.push(venueToSave);
    }

    if (processedVenues.length === 0) {
      return NextResponse.json(
        { error: "LLM generated no suitable venues from search results." },
        { status: 404 },
      );
    }

    return NextResponse.json({ places: processedVenues });
  } catch (error) {
    console.error("Error with LLM + MCP venue generation:", error);
    return NextResponse.json(
      {
        error: `Failed to generate venues using LLM + MCP: ${error instanceof Error ? error.message : String(error)}`,
      },
      { status: 500 },
    );
  }
}

// Helper functions to provide default cost/duration based on venue type if LLM doesn't infer
function getDefaultCostForType(types: string[] | undefined): number {
  if (!types) return 25; // General default
  if (types.includes("restaurant")) return 40;
  if (types.includes("bar") || types.includes("pub")) return 25;
  if (types.includes("night_club")) return 60;
  if (types.includes("cafe")) return 15;
  if (types.includes("karaoke")) return 35;
  return 25;
}

function getDefaultDurationForType(types: string[] | undefined): number {
  if (!types) return 90; // General default
  if (types.includes("restaurant")) return 90;
  if (types.includes("bar") || types.includes("pub")) return 120;
  if (types.includes("night_club")) return 180;
  if (types.includes("cafe")) return 60;
  if (types.includes("karaoke")) return 120;
  return 90;
}
