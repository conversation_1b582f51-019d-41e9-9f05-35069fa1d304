import { NextResponse } from "next/server";
import {
  db,
  auth,
  __app_id,
  authReadyPromise,
} from "../../../lib/firebase/firebase"; // Import authReadyPromise
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  Timestamp,
} from "firebase/firestore";
import { Routine, RoutineVenue, VenueData } from "@/types/routine"; // Import VenueData from shared types

// Define the structure for incoming request body
interface GenerateRoutineRequestBody {
  location: string;
  totalBudget: number;
  totalDurationMinutes: number;
  desiredActivities: string[]; // e.g., ['restaurant', 'bar', 'karaoke']
  startingTime: string; // ISO string for start time, e.g., "2025-08-04T19:00:00Z"
}

// Helper function to estimate travel time between two points (very basic straight-line distance)
// In a real app, you'd use a routing API (like OpenRouteService, Mapbox, etc., or Google Directions API if you enable billing)
function estimateTravelTimeMinutes(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  const R = 6371e3; // metres
  const φ1 = (lat1 * Math.PI) / 180; // φ, λ in radians
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c; // in metres

  // Assume average walking speed of 5 km/h (approx 83 m/min)
  // Or driving speed of 30 km/h (approx 500 m/min)
  const speedMetersPerMinute = 30000 / 60; // 30 km/h in meters per minute for driving/taxi
  const travelTime = distance / speedMetersPerMinute; // in minutes

  return Math.max(5, Math.round(travelTime)); // Minimum 5 minutes travel time
}

export async function POST(request: Request) {
  // Ensure Firebase Auth is fully initialized and ready
  // Await the authReadyPromise to ensure the 'auth' object's currentUser is populated.
  await authReadyPromise;

  const userId = auth.currentUser?.uid; // Now, auth.currentUser should be reliably available
  const __app_id = process.env.NEXT_PUBLIC_FIREBASE_APP_ID;
  if (!userId) {
    console.error(
      "Authentication failed: User ID not available after authReadyPromise.",
    );
    return NextResponse.json(
      { error: "Authentication required to generate a routine." },
      { status: 401 },
    );
  }
  if (!__app_id) {
    return NextResponse.json(
      { error: "App ID not available for Firestore path." },
      { status: 500 },
    );
  }

  try {
    const {
      location,
      totalBudget,
      totalDurationMinutes,
      desiredActivities,
      startingTime,
    }: GenerateRoutineRequestBody = await request.json();

    if (
      !location ||
      !totalBudget ||
      !totalDurationMinutes ||
      !desiredActivities ||
      desiredActivities.length === 0 ||
      !startingTime
    ) {
      return NextResponse.json(
        { error: "Missing required routine parameters." },
        { status: 400 },
      );
    }

    // Convert startingTime string to Firestore Timestamp
    const initialStartTime = Timestamp.fromDate(new Date(startingTime));
    let currentArrivalTime = new Date(startingTime);
    let currentBudget = totalBudget;
    let currentDuration = 0; // Total duration including travel
    const selectedVenues: RoutineVenue[] = [];
    let lastVenueLocation: { latitude: number; longitude: number } | null =
      null;

    // --- Routine Generation Logic (Simple Greedy Approach) ---
    for (const activityType of desiredActivities) {
      // Query Firestore for venues matching the activity type and location
      // Filter by estimatedCostPerPerson to find suitable venues within budget
      const venuesRef = collection(
        db,
        `artifacts/${__app_id}/public/data/venues`,
      );
      const q = query(
        venuesRef,
        where("types", "array-contains", activityType.toLowerCase()), // Nominatim 'type' can be lowercase
        where("estimatedCostPerPerson", "<=", currentBudget), // Basic budget check
      );

      const querySnapshot = await getDocs(q);
      const candidateVenues = querySnapshot.docs.map(
        (doc) => doc.data() as VenueData,
      );

      // Filter venues by location (Nominatim results might be broad, refine here)
      // This is a very basic string match. For real location filtering, you'd need
      // to convert the 'location' string to coordinates and filter by radius.
      const filteredVenues = candidateVenues.filter(
        (venue) =>
          venue.address.toLowerCase().includes(location.toLowerCase()) ||
          venue.name.toLowerCase().includes(location.toLowerCase()),
      );

      // Sort candidates by some criteria (e.g., lowest cost first, or highest rating if available)
      // For now, let's sort by lowest cost to maximize number of venues
      filteredVenues.sort(
        (a, b) =>
          (a.estimatedCostPerPerson || 0) - (b.estimatedCostPerPerson || 0),
      );

      let selectedVenue: VenueData | undefined;
      let travelTime = 0;

      for (const candidate of filteredVenues) {
        // Calculate travel time from last venue (if any)
        if (lastVenueLocation) {
          travelTime = estimateTravelTimeMinutes(
            lastVenueLocation.latitude,
            lastVenueLocation.longitude,
            candidate.latitude,
            candidate.longitude,
          );
        }

        const venueDuration = candidate.averageDurationMinutes || 60; // Default to 60 mins if not set
        const venueCost = candidate.estimatedCostPerPerson || 0;

        // Check if adding this venue exceeds total duration or budget
        if (
          currentDuration + travelTime + venueDuration <=
            totalDurationMinutes &&
          currentBudget - venueCost >= 0
        ) {
          selectedVenue = candidate;
          break; // Found a suitable venue, move to next activity
        }
      }

      if (selectedVenue) {
        currentDuration +=
          travelTime + (selectedVenue.averageDurationMinutes || 60);
        currentBudget -= selectedVenue.estimatedCostPerPerson || 0;

        selectedVenues.push({
          venueId: selectedVenue.id,
          venueName: selectedVenue.name,
          venueAddress: selectedVenue.address,
          venueTypes: selectedVenue.types || [],
          estimatedCost: selectedVenue.estimatedCostPerPerson || 0,
          durationMinutes: selectedVenue.averageDurationMinutes || 60,
          arrivalTime: Timestamp.fromDate(currentArrivalTime),
          latitude: selectedVenue.latitude, // Include for future mapping/routing
          longitude: selectedVenue.longitude, // Include for future mapping/routing
        });

        // Update arrival time for the *next* venue
        currentArrivalTime.setMinutes(
          currentArrivalTime.getMinutes() +
            travelTime +
            (selectedVenue.averageDurationMinutes || 60),
        );
        lastVenueLocation = {
          latitude: selectedVenue.latitude,
          longitude: selectedVenue.longitude,
        };
      } else {
        // If no suitable venue found for an activity, mark routine as failed or continue without it
        console.warn(
          `Could not find a suitable venue for activity: ${activityType} in ${location}`,
        );
        // For now, we'll just skip this activity, but you might want to return an error
        // or a partial routine.
      }
    }

    if (selectedVenues.length === 0) {
      return NextResponse.json(
        {
          error:
            "Could not generate a routine with the given criteria. Try adjusting budget, duration, or activities.",
        },
        { status: 404 },
      );
    }

    // Construct the final Routine object
    const newRoutine: Routine = {
      userId: userId,
      name: `Night Out in ${location}`, // You might let the user name this later
      description: `Generated routine for ${desiredActivities.join(", ")}`,
      location: location,
      totalBudget: totalBudget,
      totalDurationMinutes: totalDurationMinutes,
      desiredActivities: desiredActivities,
      startingTime: initialStartTime,
      generatedAt: Timestamp.now(),
      venues: selectedVenues,
      totalCalculatedCost: totalBudget - currentBudget, // Cost actually spent
      totalCalculatedDurationMinutes: currentDuration,
      status: "draft",
    };

    // Save the new routine to Firestore
    const routinesCollectionRef = collection(
      db,
      `artifacts/${__app_id}/users/${userId}/routines`,
    );
    const docRef = await addDoc(routinesCollectionRef, newRoutine);

    // Add the Firestore ID to the routine object before returning
    newRoutine.id = docRef.id;

    return NextResponse.json(newRoutine, { status: 200 });
  } catch (error) {
    console.error("Error generating routine:", error);
    return NextResponse.json(
      { error: "Failed to generate routine." },
      { status: 500 },
    );
  }
}
