import { Timestamp } from "firebase/firestore";

export interface VenueData {
  id: string; // OSM ID
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating?: number;
  priceLevel?: number; // 0-4
  types?: string[];
  phoneNumber?: string;
  website?: string;
  estimatedCostPerPerson?: number; // Your app's calculation
  averageDurationMinutes?: number; // Your app's estimate
  lastFetched?: Date; // Timestamp of last API fetch
}

export interface RoutineVenue {
  venueId: string; // Reference to the venue stored in 'public/data/venues'
  venueName: string;
  venueAddress: string;
  venueTypes: string[];
  estimatedCost: number; // Cost for this specific venue
  durationMinutes: number; // Planned duration at this venue
  arrivalTime: Timestamp; // When the user is expected to arrive at this venue
  notes?: string; // Any specific notes for this venue in the routine
  // Add other relevant fields from VenueData if needed for display/logic in routine
  latitude: number;
  longitude: number;
}

export interface Routine {
  id?: string; // Firestore document ID (will be generated by Firestore)
  userId: string; // ID of the user who created/owns this routine
  name: string; // e.g., "Friday Night Fun", "Chill Evening"
  description?: string;
  location: string; // The primary city/area for the routine
  totalBudget: number; // User's total budget for the routine
  totalDurationMinutes: number; // User's desired total duration
  desiredActivities: string[]; // e.g., ["restaurant", "bar", "karaoke"]
  startingTime: Timestamp; // User's desired start time for the routine
  generatedAt: Timestamp; // When the routine was generated
  venues: RoutineVenue[]; // Array of venues in the routine order
  totalCalculatedCost: number; // Sum of estimatedCost from all venues
  totalCalculatedDurationMinutes: number; // Sum of durationMinutes from all venues + estimated travel time
  status: "draft" | "active" | "completed" | "failed"; // Routine status
}
