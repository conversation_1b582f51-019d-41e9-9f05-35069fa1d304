import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import globals from "globals"; // Import globals for browser/node environments
import prettierPlugin from "eslint-plugin-prettier"; // Import the Prettier plugin
import prettierConfig from "eslint-config-prettier"; // Import the Prettier config
import tseslint from "typescript-eslint"; // Import typescript-eslint

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Base Next.js and TypeScript configurations
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  // TypeScript ESLint recommended rules
  ...tseslint.configs.recommended,

  // Global environments (e.g., browser, node)
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
      parser: tseslint.parser, // Use TypeScript parser
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        ecmaVersion: 2021,
        sourceType: "module",
      },
    },
    plugins: {
      "@typescript-eslint": tseslint.plugin,
      prettier: prettierPlugin, // Register the prettier plugin
    },
    rules: {
      "prettier/prettier": "error", // Enforce Prettier rules as errors
      "@typescript-eslint/no-unused-vars": "warn", // Warn on unused variables
      "@typescript-eslint/no-explicit-any": "warn", // Warn on usage of 'any' type
      // Add any other custom ESLint rules here
    },
  },

  // Prettier configuration (must be last to override other formatting rules)
  prettierConfig,
];

export default eslintConfig;
